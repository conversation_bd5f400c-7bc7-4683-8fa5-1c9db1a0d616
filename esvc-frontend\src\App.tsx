import { BrowserRouter as Router, Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { useEffect } from 'react';
import './styles/App.css';
import Header from './components/Header';
import Hero from './components/Hero';
import TreasuryDashboard from './components/TreasuryDashboard';
import ComparisonTable from './components/ComparisonTable';
import FAQ from './components/FAQ';
import Footer from './components/Footer';
import SignUp from './components/SignUp';
import ForgotPassword from './components/ForgotPassword';
import VerifyEmail from './components/VerifyEmail';
import Overview from './components/Overview';
import LiveReserve from './components/LiveReserve';
import DashboardLayout from './components/DashboardLayout';

// Scroll to top component
const ScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location]);

  return null;
};

// Landing Page Component
const LandingPage = () => {
  return (
    <DashboardLayout className="landing-page">
      <Hero />
      <TreasuryDashboard />
      <ComparisonTable />
      <FAQ />
    </DashboardLayout>
  );
};

// Auth Page Component
const AuthPage = ({ children }: { children: React.ReactNode }) => {
  return (
    <DashboardLayout className="auth-page">
      <main className="auth-main">
        {children}
      </main>
    </DashboardLayout>
  );
};

function App() {
  return (
    <Router>
      <ScrollToTop />
      <div className="app">
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/signup" element={
            <AuthPage>
              <SignUp initialMode="signup" />
            </AuthPage>
          } />
          <Route path="/login" element={
            <AuthPage>
              <SignUp initialMode="login" />
            </AuthPage>
          } />
          <Route path="/forgot-password" element={
            <AuthPage>
              <ForgotPassword onBack={() => window.history.back()} />
            </AuthPage>
          } />
          <Route path="/verify-email" element={
            <AuthPage>
              <VerifyEmail onBack={() => window.history.back()} />
            </AuthPage>
          } />
          <Route path="/overview" element={<Overview />} />
          <Route path="/live-reserve" element={<LiveReserve />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
