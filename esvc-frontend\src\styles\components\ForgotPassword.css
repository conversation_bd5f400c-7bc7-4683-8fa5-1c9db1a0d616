/* Forgot Password Container */
.forgot-password-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}



/* Main Content */
.forgot-password-content {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  padding: 10px 24px 40px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

/* Back Button */
.back-button {
  position: absolute;
  top: 30px;
  left: 24px;
  background: none;
  border: none;
  color: #D19049;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: color 0.3s ease;
}

.back-button:hover {
  color: #BF4129;
}

.back-button span {
  font-size: 18px;
}

/* Form Container */
.forgot-password-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  gap: 40px;
  position: absolute;
  width: 560px;
  height: 347px;
  left: calc(50% - 560px/2);
  top: 80px;
  background: #262626;
  border-radius: 24px;
  text-align: center;
}

/* Verify Email Form - Taller */
.forgot-password-container .forgot-password-form.verify-form {
  height: 528px;
}

.forgot-password-form h1 {
  color: #FFFFFF;
  font-size: 36px;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.forgot-password-form p {
  color: #CCCCCC;
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  line-height: 1.6;
}

/* Form Groups */
.form-group {
  width: 100%;
  text-align: left;
}

.form-group label {
  display: block;
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  height: 56px;
  background: #404040;
  border: 1px solid #525252;
  border-radius: 12px;
  padding: 0 20px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

/* Password inputs need extra right padding for toggle button */
.input-wrapper .form-input[type="password"],
.input-wrapper .form-input[type="text"] {
  padding-right: 48px;
}

.form-input::placeholder {
  color: #999999;
}

/* Password Toggle */
.toggle-password {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  width: 16px;
  height: 16px;
  color: #888888;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

.toggle-password:hover {
  color: #CCCCCC;
}

.form-input:focus {
  outline: none;
  border-color: #BF4129;
  background: #4A4A4A;
}

.form-input::placeholder {
  color: #999999;
}

.toggle-password {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #999999;
  cursor: pointer;
  font-size: 18px;
}

/* Verification Code */
.verification-code {
  display: flex;
  gap: 12px;
  justify-content: center;
  width: 100%;
}

.code-input {
  width: 56px;
  height: 56px;
  background: #404040;
  border: 1px solid #525252;
  border-radius: 12px;
  text-align: center;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.code-input:focus {
  outline: none;
  border-color: #BF4129;
  background: #4A4A4A;
}

.code-input.invalid {
  border-color: #FF4444;
  background: rgba(255, 68, 68, 0.1);
}

/* Error Message */
.error-message {
  color: #FF4444;
  font-size: 14px;
  margin: 16px 0 0 0;
  text-align: center;
}

/* Primary Button */
.btn-primary {
  width: 100%;
  height: 56px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover:not(:disabled) {
  background: #D19049;
  transform: translateY(-2px);
}

.btn-primary:disabled {
  background: #666666;
  cursor: not-allowed;
  transform: none;
}

/* Resend Section */
.resend-section {
  text-align: center;
  width: 100%;
}

.resend-section p {
  color: #CCCCCC;
  font-size: 14px;
  margin: 0 0 8px 0;
}

.resend-link {
  background: none;
  border: none;
  color: #BF4129;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

.resend-link:hover:not(:disabled) {
  color: #D19049;
}

.resend-link:disabled {
  color: #666666;
  cursor: not-allowed;
  text-decoration: none;
}

/* Change Email Button */
.change-email-btn {
  background: none;
  border: none;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  transition: color 0.3s ease;
}

.change-email-btn:hover {
  color: #FFFFFF;
}

/* Password Requirements */
.password-requirements {
  width: 100%;
  text-align: left;
}

.requirement {
  color: #999999;
  font-size: 14px;
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.requirement::before {
  content: '○';
  color: #666666;
  font-size: 12px;
}

.requirement.valid {
  color: #4CAF50;
}

.requirement.valid::before {
  content: '●';
  color: #4CAF50;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .forgot-password-content {
    padding: 5px 16px 30px;
  }

  .back-button {
    top: 20px;
    left: 16px;
  }

  .forgot-password-form {
    width: calc(100% - 32px);
    left: 16px;
    right: 16px;
    height: auto;
    min-height: 347px;
    padding: 24px;
    gap: 24px;
  }

  .forgot-password-form.verify-form {
    min-height: 528px;
  }

  .forgot-password-content {
    padding: 5px 16px 30px;
  }

  .forgot-password-form h1 {
    font-size: 28px;
  }

  .forgot-password-form p {
    font-size: 16px;
  }

  .verification-code {
    gap: 8px;
  }

  .code-input {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
}
